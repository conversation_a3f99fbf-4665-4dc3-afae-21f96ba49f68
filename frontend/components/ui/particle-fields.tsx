// components/visuals/particle-field.tsx
"use client"

import { useEffect, useRef } from "react"
import { createTimeline, stagger, utils } from "animejs"

const COUNT = 2500
const DURATION = 3000
const { random, cos, sin, sqrt, PI } = Math

export default function ParticleField() {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const win = {
      w: container.offsetWidth,
      h: container.offsetHeight,
    }

    const target = { x: 0, y: 0, r: win.w * 0.25 }
    const radius = Symbol("radius")
    const theta = Symbol("theta")

    const elements: HTMLElement[] = []

    for (let i = 0; i < COUNT; i++) {
      const dot = document.createElement("div")
      const h = utils.random(15, 25)
      const l = utils.random(10, 18)
      dot.className = "absolute rounded-full"
      dot.style.width = dot.style.height = `${1 + Math.random() * 2.5}px`
      dot.style.background = `hsl(${h}, 60%, ${l}%)`
      dot.style.pointerEvents = "none"

      dot[theta] = random() * PI * 2
      dot[radius] = target.r * sqrt(random())

      container.appendChild(dot)
      elements.push(dot)
    }

    const tl = createTimeline({
      defaults: {
        loop: true,
        ease: "inOut(1.3)",
        onLoop: (self: any) => self.refresh(),
      },
    })

    tl.add('div', {
      x: $el => target.x + ($el[radius] * cos($el[theta])),
      y: $el => target.y + ($el[radius] * sin($el[theta])),
      duration: () => DURATION + utils.random(-100, 100),
      onLoop: self => {
        const t = self.targets[0]
        t[theta] = random() * PI * 2
        t[radius] = target.r * sqrt(random())
        self.refresh()
      },
    }, stagger((DURATION / COUNT) * 1.125))
      .add(target, {
        r: () => win.w * utils.random(0.05, 0.5, 2),
        duration: 1250,
      }, 0)
      .add(target, {
        x: () => utils.random(-win.w, win.w),
        modifier: (x: any) => x + sin(tl.currentTime * 0.0007) * (win.w * 0.65),
        duration: 2800,
      }, 0)
      .add(target, {
        y: () => utils.random(-win.h, win.h),
        modifier: (y: any) => y + cos(tl.currentTime * 0.00012) * (win.h * 0.65),
        duration: 1800,
      }, 0)

    tl.seek(20000)

    return () => {
      tl.pause()
      elements.forEach(el => el.remove())
    }
  }, [])

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 overflow-hidden pointer-events-none z-0"
      style={{ position: "relative", width: "100%", height: "100%" }}
    />
  )
}
