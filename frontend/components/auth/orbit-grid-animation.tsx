"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface OrbitGridAnimationProps {
  className?: string
}

const DOTS = 13

export function OrbitAgentPulse({ className = "" }: OrbitGridAnimationProps) {
  const dots: JSX.Element[] = []
  const R = (DOTS - 1) / 2

  for (let y = 0; y < DOTS; y++) {
    for (let x = 0; x < DOTS; x++) {
      const dx = x - R
      const dy = y - R
      const inCircle = dx * dx + dy * dy <= R * R + 0.3
      if (!inCircle) continue

      const delay = Math.sqrt(dx * dx + dy * dy) * 0.1

      dots.push(
        <motion.div
          key={`${x}-${y}`}
          className="size-[6px] rounded-full bg-foreground"
          style={{
            gridColumn: x + 1,
            gridRow: y + 1,
            zIndex: 1,
          }}
          animate={{
            scale: [1, 1.5, 0.8, 1],
            opacity: [0.4, 1, 0.3, 0.4],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: delay,
            ease: "easeInOut"
          }}
        />
      )
    }
  }

  return (
    <div className={cn("relative flex items-center justify-center", className)}>
      {/* Orbit rings */}
      <motion.svg
        className="pointer-events-none absolute size-[200px]"
        viewBox="0 0 200 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style={{ zIndex: 0 }}
        animate={{
          rotate: [0, 360]
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear"
        }}
      >
        <circle 
          cx="100" 
          cy="100" 
          r="95" 
          stroke="currentColor" 
          strokeWidth="1.5" 
          opacity="0.2"
          className="text-foreground"
        />
        <circle 
          cx="100" 
          cy="100" 
          r="75" 
          stroke="currentColor" 
          strokeWidth="1.2" 
          opacity="0.15"
          className="text-foreground"
        />
        <circle 
          cx="100" 
          cy="100" 
          r="55" 
          stroke="currentColor" 
          strokeWidth="1" 
          opacity="0.1"
          className="text-foreground"
        />
      </motion.svg>

      {/* Dots grid */}
      <div
        className="relative grid"
        style={{
          gridTemplateColumns: `repeat(${DOTS}, 1fr)`,
          gridTemplateRows: `repeat(${DOTS}, 1fr)`,
          width: 160,
          height: 160,
          zIndex: 1,
        }}
      >
        {dots}
      </div>

      {/* Central glow */}
      <motion.div
        className="pointer-events-none absolute left-1/2 top-1/2 size-16 -translate-x-1/2 -translate-y-1/2 rounded-full bg-foreground/10 blur-2xl"
        style={{ zIndex: 2 }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.1, 0.3, 0.1]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  )
}
  
