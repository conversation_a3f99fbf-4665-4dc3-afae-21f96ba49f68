"use client"

import { motion, useAnimationControls } from "framer-motion"
import { useEffect } from "react"
import { cn } from "@/lib/utils"

interface OrbitIconProps {
  className?: string
  animated?: boolean
  glowing?: boolean
}

export function OrbitIcon({ className, animated = false, glowing = false }: OrbitIconProps) {
  return (
    <div className={cn("relative", className)}>
      <motion.svg
        width="24"
        height="24"
        viewBox="0 0 128 128"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={cn(
          "text-foreground",
          glowing && "drop-shadow-[0_0_8px_currentColor] filter"
        )}
        animate={animated ? {
          rotate: [0, 360],
          scale: [1, 1.05, 1],
        } : {}}
        transition={{
          rotate: {
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          },
          scale: {
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }
        }}
      >
        {/* Outer ring */}
        <motion.circle
          cx="64"
          cy="64"
          r="45"
          stroke="currentColor"
          strokeWidth="0.8"
          fill="none"
          opacity="0.4"
          strokeDasharray="2 4"
          animate={animated ? {
            rotate: [0, -360],
            strokeDashoffset: [0, -8]
          } : {}}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: "64px 64px" }}
        />
        
        {/* Middle ring */}
        <motion.circle
          cx="64"
          cy="64"
          r="32"
          stroke="currentColor"
          strokeWidth="1"
          fill="none"
          opacity="0.6"
          strokeDasharray="3 3"
          animate={animated ? {
            rotate: [0, 360],
            strokeDashoffset: [0, 6]
          } : {}}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: "64px 64px" }}
        />
        
        {/* Inner ring */}
        <circle
          cx="64"
          cy="64"
          r="20"
          stroke="currentColor"
          strokeWidth="1.2"
          fill="none"
          opacity="0.8"
        />

        {/* Central core */}
        <motion.circle
          cx="64"
          cy="64"
          r="6"
          fill="currentColor"
          opacity="0.9"
          animate={animated ? {
            scale: [1, 1.2, 1],
            opacity: [0.9, 1, 0.9]
          } : {}}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Orbital dots */}
        <motion.circle
          cx="96"
          cy="64"
          r="2"
          fill="currentColor"
          opacity="0.7"
          animate={animated ? {
            rotate: [0, 360]
          } : {}}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: "64px 64px" }}
        />
        
        <motion.circle
          cx="64"
          cy="19"
          r="1.5"
          fill="currentColor"
          opacity="0.5"
          animate={animated ? {
            rotate: [0, -360]
          } : {}}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: "64px 64px" }}
        />
        
        <motion.circle
          cx="32"
          cy="64"
          r="1"
          fill="currentColor"
          opacity="0.4"
          animate={animated ? {
            rotate: [0, 360]
          } : {}}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: "64px 64px" }}
        />

        {/* Energy lines */}
        <g opacity="0.3">
          <line x1="64" y1="10" x2="64" y2="25" stroke="currentColor" strokeWidth="0.5" />
          <line x1="64" y1="103" x2="64" y2="118" stroke="currentColor" strokeWidth="0.5" />
          <line x1="10" y1="64" x2="25" y2="64" stroke="currentColor" strokeWidth="0.5" />
          <line x1="103" y1="64" x2="118" y2="64" stroke="currentColor" strokeWidth="0.5" />
        </g>

        {/* Corner accents */}
        <g opacity="0.2">
          <circle cx="20" cy="20" r="1" fill="currentColor" />
          <circle cx="108" cy="20" r="1" fill="currentColor" />
          <circle cx="20" cy="108" r="1" fill="currentColor" />
          <circle cx="108" cy="108" r="1" fill="currentColor" />
        </g>
      </motion.svg>
      
      {/* Optional glow effect overlay */}
      {glowing && animated && (
        <motion.div
          className="absolute inset-0 rounded-full bg-current opacity-10 blur-sm"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.2, 0.1]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )}
    </div>
  )
}

// For chatbot usage - keep the OrbitPulseGrid with cool aesthetic colors
export function OrbitPulseGrid({
  className,
  size = 200,
  dotCount = 13,
  duration = 1200
}: {
  className?: string
  size?: number
  dotCount?: number
  duration?: number
}) {
  const controls = useAnimationControls()
  const mid = Math.floor(dotCount / 2)
  const spacing = size / dotCount
  const dots: { key: string; x: number; y: number; delay: number }[] = []

  for (let row = 0; row < dotCount; row++) {
    for (let col = 0; col < dotCount; col++) {
      const dx = col - mid
      const dy = row - mid
      const dist = Math.sqrt(dx * dx + dy * dy)

      if (dist <= mid + 0.2) {
        const x = dx * spacing
        const y = dy * spacing
        const delay = dist * 0.08
        dots.push({ key: `${row}-${col}`, x, y, delay })
      }
    }
  }

  useEffect(() => {
    controls.start(i => ({
      scale: [1, 1.5, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: duration / 1000,
        ease: "easeInOut",
        repeat: Infinity,
        delay: i.delay
      }
    }))
  }, [controls, duration])

  return (
    <div
      className={cn("relative flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      {dots.map((dot, idx) => (
        <motion.div
          key={dot.key}
          custom={dot}
          animate={controls}
          className="absolute bg-slate-600 dark:bg-slate-300 rounded-full"
          style={{
            width: 6,
            height: 6,
            left: `calc(50% + ${dot.x}px - 3px)`,
            top: `calc(50% + ${dot.y}px - 3px)`,
          }}
        />
      ))}
    </div>
  )
}