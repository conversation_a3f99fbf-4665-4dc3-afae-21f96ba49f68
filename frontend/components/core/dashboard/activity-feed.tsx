"use client"

import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Cpu, 
  Plus, 
  FileText, 
  ClipboardList, 
  ArrowUp,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { DashboardSummary } from '@/lib/api/dashboard-api';

// ActivityItem type moved inline
interface ActivityItem {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  user: string;
  icon?: string;
}

interface ActivityFeedProps {
  dashboardData: DashboardSummary;
  className?: string;
}

const iconMap = {
  brain: Cpu,
  plus: Plus,
  file: FileText,
  form: ClipboardList,
  'arrow-up': ArrowUp,
};

const typeConfig = {
  ai_processed: {
    color: 'bg-blue-100 text-blue-700',
    bgColor: 'bg-blue-50',
    label: 'AI Processing'
  },
  new_deal: {
    color: 'bg-emerald-100 text-emerald-700',
    bgColor: 'bg-emerald-50',
    label: 'New Deal'
  },
  new_thesis: {
    color: 'bg-violet-100 text-violet-700',
    bgColor: 'bg-violet-50',
    label: 'Thesis Update'
  },
  form_submission: {
    color: 'bg-amber-100 text-amber-700',
    bgColor: 'bg-amber-50',
    label: 'Form Submission'
  },
  info: {
    color: 'bg-gray-100 text-gray-700',
    bgColor: 'bg-gray-50',
    label: 'Info'
  },
  success: {
    color: 'bg-green-100 text-green-700',
    bgColor: 'bg-green-50',
    label: 'Success'
  }
};

export function ActivityFeed({ dashboardData, className }: ActivityFeedProps) {
  // Generate mock activities based on dashboard data
  const activities: ActivityItem[] = [
    {
      id: '1',
      type: 'info',
      title: 'Dashboard Summary',
      description: `${dashboardData.active_deals} active deals, ${dashboardData.forms} forms, ${dashboardData.theses} theses`,
      timestamp: new Date().toISOString(),
      user: 'System',
      icon: 'brain'
    },
    ...(dashboardData.ai_activity.active ? [{
      id: '2',
      type: 'success',
      title: 'AI Processing Active',
      description: `Last sync: ${dashboardData.ai_activity.last_sync || 'Recently'}`,
      timestamp: dashboardData.ai_activity.last_sync || new Date().toISOString(),
      user: 'AI System',
      icon: 'brain'
    }] : [{
      id: '2',
      type: 'info',
      title: 'AI Processing Inactive',
      description: 'AI processing will begin once you have active deals',
      timestamp: new Date().toISOString(),
      user: 'System',
      icon: 'brain'
    }])
  ];
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      x: -20,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      x: 0,
      scale: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || Clock;
    return <IconComponent className="h-4 w-4" />;
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="text-xl font-bold flex items-center gap-3">
          <Clock className="h-6 w-6" />
          Recent Activity
        </CardTitle>
        <p className="text-base text-muted-foreground">
          Latest updates and AI processing results
        </p>
      </CardHeader>
      <CardContent>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-4"
        >
          {activities.map((activity, index) => {
            const config = typeConfig[activity.type] || {
              color: 'bg-gray-100 text-gray-700',
              bgColor: 'bg-gray-50',
              label: 'Activity'
            };
            
            return (
              <motion.div
                key={activity.id}
                variants={itemVariants}
                className="group"
              >
                <div className="flex items-start gap-4 p-4 rounded-xl transition-all duration-200 hover:bg-muted/50">
                  <Avatar className="h-12 w-12 border-2 border-background shadow-sm">
                    <AvatarFallback className={cn("text-sm", config.color)}>
                      {getIcon(activity.icon || activity.type)}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="text-base font-semibold text-foreground group-hover:text-primary transition-colors">
                        {activity.title}
                      </h4>
                      <Badge
                        variant="secondary"
                        className={cn("text-sm px-3 py-1", config.color)}
                      >
                        {config.label}
                      </Badge>
                    </div>

                    <p className="text-base text-muted-foreground mb-3 line-clamp-2">
                      {activity.description}
                    </p>

                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>{activity.timestamp}</span>
                    </div>
                  </div>
                  
                  <div className="flex-shrink-0">
                    <div className={cn(
                      "w-2 h-2 rounded-full transition-all duration-200",
                      config.color.includes('blue') ? 'bg-blue-400' :
                      config.color.includes('emerald') ? 'bg-emerald-400' :
                      config.color.includes('violet') ? 'bg-violet-400' :
                      'bg-amber-400',
                      "group-hover:scale-125"
                    )} />
                  </div>
                </div>
                
                {/* Separator line */}
                {index < activities.length - 1 && (
                  <div className="ml-8 mt-2 mb-2">
                    <div className="h-px bg-border" />
                  </div>
                )}
              </motion.div>
            );
          })}
        </motion.div>
        
        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.3 }}
          className="mt-6 pt-4 border-t"
        >
          <button className="w-full text-sm text-muted-foreground hover:text-foreground transition-colors py-2 rounded-md hover:bg-muted/50">
            View all activity →
          </button>
        </motion.div>
      </CardContent>
    </Card>
  );
}
