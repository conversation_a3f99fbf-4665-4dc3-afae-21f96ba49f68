<!DOCTYPE html>
<html>
<head><title>CORS Test</title></head>
<body>
    <h1>CORS Test</h1>
    <button onclick="test()">Test Request</button>
    <div id="result"></div>
    <script>
        async function test() {
            try {
                const response = await fetch('http://localhost:8000/api/v1/onboard/46RPE6TP/validate');
                const data = await response.json();
                document.getElementById('result').innerHTML = `✅ SUCCESS: ${JSON.stringify(data)}`;
            } catch (error) {
                document.getElementById('result').innerHTML = `❌ ERROR: ${error.message}`;
                console.error('CORS Error:', error);
            }
        }
    </script>
</body>
</html> 