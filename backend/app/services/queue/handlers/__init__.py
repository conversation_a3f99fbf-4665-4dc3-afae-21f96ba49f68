"""
Queue job handlers.

This package contains handlers for queue jobs.
"""

from typing import Callable, Dict, Union  # noqa: F401

from app.services.queue.handlers.generic_job import HANDLERS as GENERIC_JOB_HANDLERS
from app.services.queue.handlers.job_tracking import H<PERSON><PERSON><PERSON><PERSON> as JOB_TRACKING_HANDLERS
from app.services.queue.handlers.submission_processing import (
    HANDLERS as SUBMISSION_PROCESSING_HANDLERS,
)
from app.services.queue.worker_interface import JobHandlerInterface
from app.jobs.chat_completion import process_chat_completion

# Create handler registry
HANDLERS: Dict[str, Union[JobHandlerInterface, Callable]] = {
    # Function-based handlers
    **JOB_TRACKING_HANDLERS,
    **GENERIC_JOB_HANDLERS,
    **SUBMISSION_PROCESSING_HANDLERS,
    # Chat completion handler
    "chat_completion": process_chat_completion,
}


# Add job tracking handlers
HANDLERS.update(JO<PERSON>_TRACKING_HANDLERS)

# Add generic job handlers
HANDLE<PERSON>.update(GENERI<PERSON>_JOB_HANDLERS)

__all__ = ["HANDLERS"]
