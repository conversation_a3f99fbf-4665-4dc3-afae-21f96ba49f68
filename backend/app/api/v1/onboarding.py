"""
Onboarding API endpoints for invite code generation and organization setup.

This module handles the viral onboarding flow:
1. Invite code generation
2. Organization creation
3. User profile setup
4. Peer invitations
"""

from fastapi import Depends, HTTPException, status

from app.api.base import BaseAPIRouter
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.models.auth import TokenData
from app.schemas.auth import (
    CreateInviteCodeRequest,
    CreateInviteCodeResponse,
    OnboardingCompleteResponse,
    OnboardingStepOneRequest,
    OnboardingStepTwoRequest,
    PeerInviteRequest,
)
from app.services.factory import get_onboarding_service, get_role_service
from app.services.onboarding.interface import IOnboardingService

logger = get_logger(__name__)

# Public router for invite code generation and onboarding
public_router = BaseAPIRouter(
    prefix="", disable_auth=True, require_org=False, tags=["onboarding"]
)

# Protected router for peer invites
protected_router = BaseAPIRouter(prefix="", tags=["onboarding"])


@public_router.post(
    "/auth/public/create-invite-code", response_model=CreateInviteCodeResponse
)
async def create_invite_code(
    request: CreateInviteCodeRequest,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Generate a unique invite code for organization onboarding.

    This endpoint creates a single-use invite code that allows the specified
    email to create a new organization and user account.
    """
    try:
        logger.info(f"Creating invite code for {request.email}")

        invite_code = await onboarding_service.create_invite_code(
            email=request.email, org_name=request.org_name
        )

        # Construct the onboarding URL
        base_url = "https://v1.tractionx.ai"
        onboard_url = f"{base_url}/onboard/{invite_code.code}"

        # Send the invite email
        from app.services.factory import get_email_service

        email_service = await get_email_service()

        await email_service.send_invite_code_email(
            email=invite_code.email,
            invite_code=invite_code.code,
            onboard_url=onboard_url,
            org_name=invite_code.org_name,
        )

        return CreateInviteCodeResponse(
            code=invite_code.code,
            email=invite_code.email,
            expires_at=invite_code.expires_at,
            onboard_url=onboard_url,
        )

    except Exception as e:
        logger.error(f"Error creating invite code: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create invite code",
        )


@public_router.get("/onboard/{code}/validate")
async def validate_invite_code(
    code: str,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Validate an invite code and return associated information.

    This endpoint checks if an invite code is valid and returns
    the associated email and organization name (if any).
    """
    try:
        invite_code = await onboarding_service.validate_invite_code(code)

        return {
            "valid": True,
            "email": invite_code.email,
            "org_name": invite_code.org_name,
            "expires_at": invite_code.expires_at,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating invite code: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate invite code",
        )


@public_router.post("/onboard/{code}/step-1")
async def onboarding_step_one(
    code: str,
    request: OnboardingStepOneRequest,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Step 1: Create organization during onboarding.

    This endpoint creates the organization with the provided details
    and validates the subdomain availability.
    """
    try:
        logger.info(f"Onboarding step 1 for code {code}")

        # Validate invite code
        invite_code = await onboarding_service.validate_invite_code(code)

        # Create organization
        organization = await onboarding_service.create_organization(
            invite_code=invite_code,
            org_name=request.org_name,
            subdomain=request.subdomain,
            website=request.website,
            logo_url=request.logo_url,
        )
        role_service = await get_role_service()
        gp_role, analyst_role = await role_service.create_default_roles(  # type: ignore
            str(organization.id)
        )

        return {
            "success": True,
            "org_id": str(organization.id),
            "message": "Organization created successfully",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in onboarding step 1: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create organization",
        )


@public_router.post("/onboard/{code}/step-2", response_model=OnboardingCompleteResponse)
async def onboarding_step_two(
    code: str,
    request: OnboardingStepTwoRequest,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Step 2: Create user profile and complete onboarding.

    This endpoint creates the user profile, completes the onboarding
    process, and returns authentication tokens.
    """
    try:
        logger.info(f"Onboarding step 2 for code {code}")

        # Validate invite code
        invite_code = await onboarding_service.validate_invite_code(code)

        # Get the organization created in step 1
        from app.models.organization import Organization

        organization = await Organization.find_one({"contact_email": invite_code.email})

        if not organization:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Organization not found. Please complete step 1 first.",
            )

        # Create user profile
        user = await onboarding_service.create_user_profile(
            invite_code=invite_code,
            organization=organization,
            name=request.name,
            password=request.password,
            profile_picture_url=request.profile_picture_url,
        )

        # Complete onboarding and get tokens
        result = await onboarding_service.complete_onboarding(
            invite_code=invite_code, user=user, organization=organization
        )

        return OnboardingCompleteResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in onboarding step 2: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete onboarding",
        )


@public_router.get("/onboard/check-subdomain/{subdomain}")
async def check_subdomain_availability(
    subdomain: str,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Check if a subdomain is available for use.

    This endpoint validates subdomain format and checks availability.
    """
    try:
        available = await onboarding_service.check_subdomain_availability(subdomain)

        return {"subdomain": subdomain, "available": available}

    except Exception as e:
        logger.error(f"Error checking subdomain availability: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check subdomain availability",
        )


@protected_router.post("/auth/public/invite-peers")
async def invite_peers(
    request: PeerInviteRequest,
    current_user: TokenData = Depends(get_current_user),
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Send invitations to peers to join the organization.

    This endpoint allows users to invite colleagues to their organization
    after completing the onboarding process.
    """
    try:
        logger.info(f"Sending peer invites from user {current_user.user_id}")

        result = await onboarding_service.send_peer_invites(
            org_id=current_user.org_id,
            invited_by=current_user.user_id,
            emails=request.emails,
            role_id=request.role_id,
            message=request.message,
        )

        return result

    except Exception as e:
        logger.error(f"Error sending peer invites: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send peer invites",
        )
